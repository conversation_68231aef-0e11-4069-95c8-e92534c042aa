
## 📝 Product Requirements Document (PRD)

### **Product Name:**

**Hateable** *(working title)*

---

### **Overview:**

Hateable is an AI-powered visual builder that allows users to create full-stack web or mobile applications using natural language prompts. Inspired by platforms like **Lovable.dev** and **Bolt.new**, it focuses on rapid prototyping, clean UI/UX, developer handoff, and extensibility via code exports or integrations.

---

### **Target Audience:**

* Indie developers and makers
* Startups and founders
* Product designers
* Agencies and freelancers
* No-code/low-code users seeking faster iteration
* PMs and non-technical stakeholders

---

### **Goals:**

* Let users build responsive UIs, workflows, and database models using prompts.
* Support export to modern tech stacks (Next.js, React, Tailwind, Supabase, etc.).
* Provide a clean visual editor for post-generation refinements.
* Enable sharing, deploying, or exporting projects easily.

---

### **Core Features:**

#### 1. **Prompt-to-App Builder**

* Natural language prompt input (e.g., “Build a job board for designers”).
* Converts prompts into:

  * Page layout with components (e.g., navbar, cards, filters)
  * Database schema (e.g., Postgres/Supabase tables)
  * Business logic and state (Zustand, Redux, etc.)
  * API routes and authentication

#### 2. **Visual Editor (WYSIWYG)**

* Drag-and-drop components with layout rules
* Sectional editing: header, body, footer, etc.
* Property panel for styles, bindings, and events

#### 3. **Component Library**

* Built-in support for:

  * **UI Kits:** shadcn/ui, Material, Chakra
  * **Charts:** Recharts, Chart.js
  * **Forms & Auth:** Magic link, Google, Apple
* Custom component creation

#### 4. **Database & Backend**

* Visual DB schema editor
* Connect with:

  * Supabase
  * Firebase
  * PostgreSQL/MySQL
* Auto-generate CRUD APIs
* Serverless function support (Edge, Vercel/Cloudflare functions)

#### 5. **Deploy & Export**

* One-click deploy to:

  * Vercel
  * Netlify
  * Render
* Export to GitHub repo or ZIP
* Embed mode for client delivery

#### 6. **Versioning & Branching**

* Save iterations
* Branch from a version (good for A/B or design explorations)

#### 7. **Collaboration**

* Real-time multi-user editing
* Comment threads
* Shareable public preview links

#### 8. **AI Chat Assistant**

* "How do I…?" queries (e.g., “Make the navbar sticky”)
* Explain code
* Generate copy (e.g., landing page hero text)

---

### **Nice-to-Have Features (V2+)**

* **Multimodal input:** sketch + prompt → UI
* **Animation engine:** Lottie / Framer Motion integration
* **Multi-platform support:** React Native, Flutter
* **Pre-built templates:** Dashboards, CRMs, Job boards
* **Figma sync plugin**
* **Marketplace:** Share/buy templates or plugins

---

### **Tech Stack (Suggested)**

* **Frontend:** Next.js + Tailwind CSS + Radix UI / shadcn/ui
* **Backend:** Node.js + Supabase or Convex for real-time
* **Auth:** Firebase Auth or Clerk
* **AI:** OpenAI / Claude / Gemini APIs (via server proxy)
* **State Mgmt:** Zustand or Redux Toolkit
* **Infra:** Vercel, Railway, or Render

---

### **Metrics for Success**

* % of users who reach deploy/export step
* Avg time from prompt to working prototype
* User NPS (Net Promoter Score)
* Code export quality satisfaction score
* Number of shared projects/templates

---

### **Competitive Analysis**

| Platform     | Strengths                                | Weaknesses                                |
| ------------ | ---------------------------------------- | ----------------------------------------- |
| Lovable.dev  | Clean UI, fast prompt-to-app flow        | Limited customization & exports           |
| Bolt.new     | Visual + prompt mix, multi-stack exports | Pricing barrier, not open source friendly |
| Builder.io   | Enterprise-friendly, Figma integration   | Heavier learning curve                    |
| Webflow + AI | Design-centric                           | Not code-export oriented                  |

---

### **Timeline (MVP)**

| Milestone                       | ETA      |
| ------------------------------- | -------- |
| Prompt Parsing & Initial Layout | Week 1–2 |
| UI Renderer & Editor            | Week 3–4 |
| DB & API Scaffold Generation    | Week 5–6 |
| Export & Deployment             | Week 7   |
| Collaboration & Preview Links   | Week 8   |
| Public Alpha                    | Week 9   |

---

### **Open Questions:**

* How customizable should the AI-generated code be?
* Should templates be community-contributed from the start?
* How to handle large projects with >10 pages?
