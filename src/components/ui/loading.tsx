import React from 'react'
import { Loader2, Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <Loader2 
      className={cn(
        'animate-spin text-primary',
        sizeClasses[size],
        className
      )} 
    />
  )
}

interface LoadingStateProps {
  title?: string
  description?: string
  className?: string
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  title = 'Loading...',
  description,
  className
}) => {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <div className="relative mb-4">
        <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
          <Sparkles className="h-6 w-6 text-primary animate-pulse" />
        </div>
        <LoadingSpinner className="absolute -top-1 -right-1 h-4 w-4" />
      </div>
      
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      {description && (
        <p className="text-sm text-muted-foreground max-w-sm">{description}</p>
      )}
    </div>
  )
}

interface FullPageLoadingProps {
  title?: string
  description?: string
}

export const FullPageLoading: React.FC<FullPageLoadingProps> = ({
  title = 'Loading your workspace...',
  description = 'Please wait while we prepare everything for you.'
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <LoadingState title={title} description={description} />
    </div>
  )
}

interface ButtonLoadingProps {
  loading?: boolean
  children: React.ReactNode
  loadingText?: string
}

export const ButtonLoading: React.FC<ButtonLoadingProps> = ({
  loading = false,
  children,
  loadingText
}) => {
  if (loading) {
    return (
      <>
        <LoadingSpinner size="sm" className="mr-2" />
        {loadingText || children}
      </>
    )
  }
  
  return <>{children}</>
}
