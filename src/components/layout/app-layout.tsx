'use client'

import React, { useState } from 'react'
import Navbar from './navbar'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { signIn } from 'next-auth/react'
import { Github, Loader } from 'lucide-react'

interface AppLayoutProps {
  children: React.ReactNode
  variant?: 'landing' | 'app'
  className?: string
}

const AppLayout: React.FC<AppLayoutProps> = ({ 
  children, 
  variant = 'app',
  className = ''
}) => {
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false)
  const [authLoading, setAuthLoading] = useState(false)

  const handleAuthAction = async (provider: 'github' | 'google') => {
    setAuthLoading(true)
    try {
      await signIn(provider)
    } catch (error) {
      console.error('Authentication failed:', error)
    } finally {
      setAuthLoading(false)
    }
  }

  return (
    <div className={`min-h-screen ${className}`}>
      <Navbar variant={variant} setIsLoginDialogOpen={setIsLoginDialogOpen} />
      
      <main className="flex-1">
        {children}
      </main>

      {/* Authentication Dialog */}
      <Dialog open={isLoginDialogOpen} onOpenChange={setIsLoginDialogOpen}>
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader className="text-center">
            <DialogTitle className="text-2xl font-bold mb-2">
              Welcome to <span className='hateable-regular bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent'>Hateable</span>
            </DialogTitle>
            <DialogDescription className="text-base">
              Sign in to start building amazing applications with AI
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-6">
            <Button
              onClick={() => handleAuthAction("github")}
              variant="outline"
              size="lg"
              disabled={authLoading}
              aria-describedby="github-auth-description"
              className="w-full h-12 text-base font-medium hover:bg-accent focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              {authLoading ? (
                <Loader className="w-5 h-5 mr-3 animate-spin" aria-hidden="true" />
              ) : (
                <Github className='w-5 h-5 mr-3' aria-hidden="true" />
              )}
              Continue with GitHub
            </Button>
            <span id="github-auth-description" className="sr-only">
              Sign in using your GitHub account to access Hateable
            </span>
            
            <Button
              onClick={() => handleAuthAction("google")}
              variant="outline"
              size="lg"
              disabled={authLoading}
              aria-describedby="google-auth-description"
              className="w-full h-12 text-base font-medium hover:bg-accent focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              {authLoading ? (
                <Loader className="w-5 h-5 mr-3 animate-spin" aria-hidden="true" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-3" viewBox="0 0 24 24" aria-hidden="true">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
              )}
              Continue with Google
            </Button>
            <span id="google-auth-description" className="sr-only">
              Sign in using your Google account to access Hateable
            </span>
          </div>
          
          <div className="text-center text-sm text-muted-foreground">
            By continuing, you agree to our Terms of Service and Privacy Policy
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AppLayout
