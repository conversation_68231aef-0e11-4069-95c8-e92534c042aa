import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Provider from "./Provider";
import { ConvexClientProvider } from "./ConvexClientProvider";
import { Toaster } from "@/components/ui/sonner";

const geistSans = Montserrat({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Montserrat({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Hateable – AI-Powered Web App Builder for Developers & Creators",
  description: "Hateable is a lightning-fast AI web app builder that lets you design, code, and deploy full-stack applications instantly. Built for developers, makers, and startups.",
  icons: {
    icon: '/favicon.ico',
  },
  keywords: [
    "AI web app builder",
    "Hateable",
    "bolt.new alternative",
    "Next.js AI builder",
    "web app generator",
    "AI coding platform",
    "low-code development",
    "full-stack app builder",
    "developer tools",
    "code generation with AI"
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON>", url: "https://www.linkedin.com/in/khus<PERSON><PERSON>i/" }],
  creator: "Hateable",
  metadataBase: new URL("https://hateable-flax.vercel.app"),
  openGraph: {
    title: "Hateable – AI-Powered Web App Builder for Developers & Creators",
    description:
      "Design, generate, and deploy complete web apps with Hateable's intelligent builder. Perfect for developers who want speed, control, and flexibility.",
    url: "https://hateable-flax.vercel.app",
    siteName: "Hateable",
    images: [
      {
        url: "https://hateable-flax.vercel.app/logo.png",
        width: 1200,
        height: 630,
        alt: "Hateable - AI Web App Builder Preview",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  alternates: {
    canonical: "https://hateable-flax.vercel.app",
  },
};


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ConvexClientProvider>
      <Provider>
        {children}
        <Toaster richColors position="top-right" />
      </Provider>
      </ConvexClientProvider>
      </body>
    </html>
    
  );
}
