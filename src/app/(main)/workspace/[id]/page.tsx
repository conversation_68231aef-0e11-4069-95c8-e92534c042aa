"use client"

import AppLayout from "@/components/layout/app-layout"
import ChatVie<PERSON> from "@/components/customs/workspace/ChatView";
import CodeView from "@/components/customs/workspace/CodeView";
import { useIsMobile } from "@/hooks/use-mobile";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Monitor, Smartphone, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";

const Page = () => {
    const isMobile = useIsMobile();
    const router = useRouter();

    if (isMobile) {
      return (
        <AppLayout variant="app">
          <div className="container mx-auto px-4 py-8">
            <Card className="max-w-md mx-auto">
              <CardContent className="p-8 text-center">
                <div className="mb-6">
                  <div className="relative mx-auto w-20 h-20 mb-4">
                    <Monitor className="w-20 h-20 text-muted-foreground" />
                    <Smartphone className="absolute -bottom-2 -right-2 w-8 h-8 text-primary" />
                  </div>
                </div>

                <h1 className="text-2xl font-bold mb-4">Desktop Required</h1>
                <p className="text-muted-foreground mb-6">
                  The workspace feature requires a desktop or tablet device for the best experience.
                  Please switch to a larger screen to continue building your app.
                </p>

                <div className="space-y-3">
                  <Button
                    onClick={() => router.push('/Thinkpad')}
                    className="w-full"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Home
                  </Button>

                  <p className="text-sm text-muted-foreground">
                    Mobile workspace support coming soon!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </AppLayout>
      );
    }

  return (
    <AppLayout variant="app" className="h-screen flex flex-col">
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          <ResizablePanel defaultSize={30} minSize={25} maxSize={50}>
            <div className="h-full p-4">
              <ChatView/>
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          <ResizablePanel defaultSize={70} minSize={50}>
            <div className="h-full p-4">
              <CodeView/>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </AppLayout>
  )
}

export default Page
