'use client'

import React, { useState, useEffect } from 'react'
import AppLayout from '@/components/layout/app-layout'
import { Textarea } from '@/components/ui/textarea'
import { ArrowRight, Sparkles, Loader, Zap, Code, Palette, Database } from 'lucide-react'
import Lookup from '@/data/Lookup'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useSession } from 'next-auth/react'
import { useMutation } from 'convex/react'
import { api } from '../../../../convex/_generated/api'
import { useRouter } from 'next/navigation'
import { Id } from '../../../../convex/_generated/dataModel'
import { useSidebar } from '@/components/ui/sidebar'
import { toast } from 'sonner'


const Page = () => {
    const [userInput, setUserInput] = useState("");
    const { toggleSidebar } = useSidebar();
    const [isLoading, setIsLoading] = useState(false);
    const { data: session, status } = useSession();
    const createWorkSpace = useMutation(api.workspace.createWorkSpace)
    const router = useRouter()

    const features = [
        { icon: Code, title: "AI Code Generation", description: "Generate full-stack applications with AI" },
        { icon: Palette, title: "Visual Editor", description: "Drag-and-drop interface for easy customization" },
        { icon: Database, title: "Database Integration", description: "Connect with popular databases seamlessly" },
        { icon: Zap, title: "One-Click Deploy", description: "Deploy to Vercel, Netlify, and more" }
    ];

    const onGenerate = async (input: string) => {
        if (!input.trim()) {
            toast.error("Please enter a prompt to get started");
            return;
        }

        if (status === "loading") {
            toast.info("Please wait while we load your session...");
            return;
        }

        if (!session || !session.user?._id) {
            toast.error("Please sign in to continue");
            return;
        }

        setIsLoading(true);
        const newMessage = { role: "user", content: input };

        try {
            toast.promise(
                createWorkSpace({
                    messages: [newMessage],
                    filedata: undefined,
                    user: session.user._id as Id<"users">,
                }).then((workspaceId) => {
                    router.push(`/workspace/${workspaceId}`);
                    return workspaceId;
                }),
                {
                    loading: 'Creating your workspace...',
                    success: 'Workspace created! Redirecting...',
                    error: 'Failed to create workspace. Please try again.',
                }
            );
        } catch (error) {
            console.error("Error creating workspace:", error);
            toast.error("Something went wrong. Please try again.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleSuggestionClick = (suggestion: string) => {
        setUserInput(suggestion);
        // Auto-focus the textarea after setting the suggestion
        setTimeout(() => {
            const textarea = document.getElementById('thinkpad-textarea');
            textarea?.focus();
        }, 100);
    };

    return (
        <AppLayout variant="app" className='bg-gradient-to-br from-background via-background to-muted/20'>

            <main className='container mx-auto px-4 py-8'>
                {/* Hero Section */}
                <div className='text-center mb-12 mt-8'>
                    <div className='inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6'>
                        <Sparkles className='w-4 h-4' />
                        AI-Powered Development Platform
                    </div>

                    <h1 className='text-4xl sm:text-6xl font-bold mb-4 bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent'>
                        Welcome to <span className='hateable-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent'>Hateable</span>
                    </h1>

                    <p className='text-lg sm:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto'>
                        Transform your ideas into full-stack applications with the power of AI.
                        Just describe what you want to build, and we'll handle the rest.
                    </p>
                </div>

                {/* Main Input Section */}
                <div className='max-w-4xl mx-auto mb-12'>
                    <Card className='border-2 border-dashed border-primary/20 hover:border-primary/40 transition-colors'>
                        <CardContent className='p-6'>
                            <div className='relative'>
                                <Textarea
                                    id='thinkpad-textarea'
                                    name='thinkpad-textarea'
                                    aria-label="Describe your app idea"
                                    aria-describedby="prompt-help-text"
                                    className="min-h-[120px] text-base resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground/60 pr-16"
                                    placeholder="Describe your app idea... (e.g., 'Create a task management app with user authentication and real-time collaboration')"
                                    value={userInput}
                                    onChange={(e) => setUserInput(e.target.value)}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter' && !e.shiftKey) {
                                            e.preventDefault();
                                            if (userInput.trim() && !isLoading) {
                                                onGenerate(userInput);
                                                setUserInput('');
                                                toggleSidebar();
                                            }
                                        }
                                    }}
                                    disabled={isLoading}
                                    maxLength={500}
                                />

                                <Button
                                    onClick={() => {
                                        if (userInput.trim() && !isLoading) {
                                            onGenerate(userInput);
                                            setUserInput('');
                                            toggleSidebar();
                                        }
                                    }}
                                    disabled={!userInput.trim() || isLoading}
                                    size="sm"
                                    aria-label={isLoading ? "Generating app..." : "Generate app"}
                                    className='absolute bottom-3 right-3 h-10 w-10 p-0 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
                                >
                                    {isLoading ? (
                                        <Loader className="h-4 w-4 animate-spin" aria-hidden="true" />
                                    ) : (
                                        <ArrowRight className="h-4 w-4" aria-hidden="true" />
                                    )}
                                </Button>
                            </div>

                            <div className='flex items-center justify-between mt-4 text-sm text-muted-foreground'>
                                <span id="prompt-help-text">Press Enter to generate or click the arrow</span>
                                <span aria-live="polite" aria-label={`Character count: ${userInput.length} of 500`}>
                                    {userInput.length}/500
                                </span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Suggestions */}
                <div className='max-w-4xl mx-auto mb-12'>
                    <h3 className='text-lg font-semibold mb-4 text-center'>Try these examples:</h3>
                    <div className='flex flex-wrap justify-center gap-2'>
                        {Lookup.SUGGESTIONS.map((suggestion, index) => (
                            <Badge
                                key={index}
                                variant="secondary"
                                className='cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors px-3 py-2 text-sm'
                                onClick={() => handleSuggestionClick(suggestion)}
                            >
                                {suggestion}
                            </Badge>
                        ))}
                    </div>
                </div>

                {/* Features Grid */}
                <div className='max-w-6xl mx-auto'>
                    <h2 className='text-2xl font-bold text-center mb-8'>Why Choose Hateable?</h2>
                    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
                        {features.map((feature, index) => (
                            <Card key={index} className='text-center hover:shadow-lg transition-shadow'>
                                <CardContent className='p-6'>
                                    <feature.icon className='w-12 h-12 mx-auto mb-4 text-primary' />
                                    <h3 className='font-semibold mb-2'>{feature.title}</h3>
                                    <p className='text-sm text-muted-foreground'>{feature.description}</p>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </main>
        </AppLayout>
    )
}

export default Page
